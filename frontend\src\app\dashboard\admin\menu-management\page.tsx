"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Navigation } from '@/components/Navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Plus,
  Edit,
  Trash2,
  GripVertical,
  Eye,
  EyeOff,
  ExternalLink,
  FileText,
  Link,
  Menu,
  ArrowLeft
} from 'lucide-react'
import { toast } from 'sonner'
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core'
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable'
import {
  useSortable,
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'

// أنواع البيانات
interface MenuItem {
  id: string
  title_ar: string
  title_en?: string
  title_fr?: string
  slug: string
  icon?: string
  parent_id?: string
  order_index: number
  is_active: boolean
  target_type: 'internal' | 'external' | 'page'
  target_value: string
  created_at: string
  updated_at: string
}

// مكون عنصر القائمة القابل للسحب
interface SortableMenuItemProps {
  item: MenuItem
  onEdit: (item: MenuItem) => void
  onDelete: (id: string) => void
  onToggleStatus: (item: MenuItem) => void
  getTargetIcon: (type: string) => React.ReactNode
  childItems: MenuItem[]
}

function SortableMenuItem({
  item,
  onEdit,
  onDelete,
  onToggleStatus,
  getTargetIcon,
  childItems
}: SortableMenuItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: item.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  }

  return (
    <div ref={setNodeRef} style={style} className="space-y-2">
      {/* Main Item */}
      <div className="flex items-center justify-between p-4 border rounded-lg bg-white dark:bg-gray-800">
        <div className="flex items-center gap-4">
          <div
            {...attributes}
            {...listeners}
            className="cursor-grab active:cursor-grabbing"
          >
            <GripVertical className="h-4 w-4 text-gray-400" />
          </div>
          <div className="flex items-center gap-2">
            {getTargetIcon(item.target_type)}
            <span className="font-medium arabic-text">{item.title_ar}</span>
            {!item.is_active && (
              <Badge variant="secondary" className="arabic-text">غير مفعل</Badge>
            )}
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onToggleStatus(item)}
          >
            {item.is_active ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onEdit(item)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDelete(item.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Child Items */}
      {childItems.map((childItem) => (
        <div key={childItem.id} className="mr-8 flex items-center justify-between p-3 border rounded-lg bg-gray-50 dark:bg-gray-700">
          <div className="flex items-center gap-2">
            {getTargetIcon(childItem.target_type)}
            <span className="arabic-text">{childItem.title_ar}</span>
            {!childItem.is_active && (
              <Badge variant="secondary" className="arabic-text">غير مفعل</Badge>
            )}
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onToggleStatus(childItem)}
            >
              {childItem.is_active ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit(childItem)}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete(childItem.id)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      ))}
    </div>
  )
}

interface MenuItemForm {
  title_ar: string
  title_en: string
  title_fr: string
  slug: string
  icon: string
  parent_id: string
  target_type: 'internal' | 'external' | 'page'
  target_value: string
  is_active: boolean
}

export default function MenuManagementPage() {
  const { user, profile } = useAuth()
  const [menuItems, setMenuItems] = useState<MenuItem[]>([])
  const [loading, setLoading] = useState(true)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingItem, setEditingItem] = useState<MenuItem | null>(null)
  const [formData, setFormData] = useState<MenuItemForm>({
    title_ar: '',
    title_en: '',
    title_fr: '',
    slug: '',
    icon: '',
    parent_id: '',
    target_type: 'internal',
    target_value: '',
    is_active: true
  })

  // إعداد sensors للسحب والإفلات
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  // التحقق من صلاحيات الأدمن
  useEffect(() => {
    if (!user || !profile || profile.role !== 'admin') {
      window.location.href = '/dashboard'
      return
    }
  }, [user, profile])

  // جلب عناصر القائمة
  const fetchMenuItems = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/menu-items?include_inactive=true')
      const data = await response.json()
      
      if (response.ok) {
        setMenuItems(data.menuItems || [])
      } else {
        toast.error(data.error || 'فشل في جلب عناصر القائمة')
      }
    } catch (error) {
      console.error('Error fetching menu items:', error)
      toast.error('خطأ في الاتصال بالخادم')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchMenuItems()
  }, [])

  // إعادة تعيين النموذج
  const resetForm = () => {
    setFormData({
      title_ar: '',
      title_en: '',
      title_fr: '',
      slug: '',
      icon: '',
      parent_id: '',
      target_type: 'internal',
      target_value: '',
      is_active: true
    })
    setEditingItem(null)
  }

  // فتح نموذج التحرير
  const openEditDialog = (item: MenuItem) => {
    setEditingItem(item)
    setFormData({
      title_ar: item.title_ar,
      title_en: item.title_en || '',
      title_fr: item.title_fr || '',
      slug: item.slug,
      icon: item.icon || '',
      parent_id: item.parent_id || '',
      target_type: item.target_type,
      target_value: item.target_value,
      is_active: item.is_active
    })
    setIsDialogOpen(true)
  }

  // حفظ عنصر القائمة
  const saveMenuItem = async () => {
    try {
      const url = editingItem 
        ? `/api/menu-items/${editingItem.id}`
        : '/api/menu-items'
      
      const method = editingItem ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          parent_id: formData.parent_id || null
        }),
      })

      const data = await response.json()

      if (response.ok) {
        toast.success(data.message)
        setIsDialogOpen(false)
        resetForm()
        fetchMenuItems()
      } else {
        toast.error(data.error || 'فشل في حفظ عنصر القائمة')
      }
    } catch (error) {
      console.error('Error saving menu item:', error)
      toast.error('خطأ في الاتصال بالخادم')
    }
  }

  // حذف عنصر القائمة
  const deleteMenuItem = async (id: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
      return
    }

    try {
      const response = await fetch(`/api/menu-items/${id}`, {
        method: 'DELETE',
      })

      const data = await response.json()

      if (response.ok) {
        toast.success(data.message)
        fetchMenuItems()
      } else {
        toast.error(data.error || 'فشل في حذف عنصر القائمة')
      }
    } catch (error) {
      console.error('Error deleting menu item:', error)
      toast.error('خطأ في الاتصال بالخادم')
    }
  }

  // تبديل حالة التفعيل
  const toggleItemStatus = async (item: MenuItem) => {
    try {
      const response = await fetch(`/api/menu-items/${item.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...item,
          is_active: !item.is_active
        }),
      })

      const data = await response.json()

      if (response.ok) {
        toast.success(data.message)
        fetchMenuItems()
      } else {
        toast.error(data.error || 'فشل في تحديث حالة العنصر')
      }
    } catch (error) {
      console.error('Error toggling item status:', error)
      toast.error('خطأ في الاتصال بالخادم')
    }
  }

  // معالجة السحب والإفلات
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event

    if (!over || active.id === over.id) {
      return
    }

    const oldIndex = mainItems.findIndex(item => item.id === active.id)
    const newIndex = mainItems.findIndex(item => item.id === over.id)

    if (oldIndex === -1 || newIndex === -1) {
      return
    }

    // إعادة ترتيب العناصر محلياً
    const newMainItems = arrayMove(mainItems, oldIndex, newIndex)

    // تحديث الحالة المحلية
    const updatedMenuItems = [...menuItems]
    const nonMainItems = menuItems.filter(item => item.parent_id)

    // دمج العناصر الرئيسية المرتبة مع العناصر الفرعية
    const reorderedItems = [...newMainItems, ...nonMainItems]
    setMenuItems(reorderedItems)

    try {
      // إرسال التحديث للخادم
      const itemsToUpdate = newMainItems.map((item, index) => ({
        id: item.id,
        order_index: index + 1
      }))

      const response = await fetch('/api/menu-items/reorder', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ items: itemsToUpdate }),
      })

      const data = await response.json()

      if (response.ok) {
        toast.success(data.message)
        // إعادة جلب البيانات للتأكد من التحديث
        fetchMenuItems()
      } else {
        toast.error(data.error || 'فشل في تحديث الترتيب')
        // إعادة الحالة السابقة في حالة الفشل
        setMenuItems(menuItems)
      }
    } catch (error) {
      console.error('Error reordering items:', error)
      toast.error('خطأ في الاتصال بالخادم')
      // إعادة الحالة السابقة في حالة الفشل
      setMenuItems(menuItems)
    }
  }

  // تصفية العناصر الرئيسية والفرعية
  const mainItems = menuItems.filter(item => !item.parent_id)
  const getChildItems = (parentId: string) =>
    menuItems.filter(item => item.parent_id === parentId)

  // أيقونات الهدف
  const getTargetIcon = (targetType: string) => {
    switch (targetType) {
      case 'internal': return <Link className="h-4 w-4" />
      case 'external': return <ExternalLink className="h-4 w-4" />
      case 'page': return <FileText className="h-4 w-4" />
      default: return <Link className="h-4 w-4" />
    }
  }

  if (!user || !profile || profile.role !== 'admin') {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Button variant="outline" size="sm" asChild>
              <a href="/dashboard/admin">
                <ArrowLeft className="h-4 w-4 mr-2" />
                العودة للوحة التحكم
              </a>
            </Button>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
                إدارة القائمة الرئيسية 🗂️
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
                تحكم في عناصر القائمة الرئيسية وترتيبها
              </p>
            </div>
            
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={resetForm}>
                  <Plus className="h-4 w-4 mr-2" />
                  إضافة عنصر جديد
                </Button>
              </DialogTrigger>
              
              <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="arabic-text">
                    {editingItem ? 'تحرير عنصر القائمة' : 'إضافة عنصر قائمة جديد'}
                  </DialogTitle>
                  <DialogDescription className="arabic-text">
                    املأ البيانات أدناه لإنشاء أو تحديث عنصر القائمة
                  </DialogDescription>
                </DialogHeader>

                <Tabs defaultValue="basic" className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="basic" className="arabic-text">البيانات الأساسية</TabsTrigger>
                    <TabsTrigger value="translations" className="arabic-text">الترجمات</TabsTrigger>
                  </TabsList>

                  <TabsContent value="basic" className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="title_ar" className="arabic-text">العنوان بالعربية *</Label>
                        <Input
                          id="title_ar"
                          value={formData.title_ar}
                          onChange={(e) => setFormData({...formData, title_ar: e.target.value})}
                          placeholder="مثال: الرئيسية"
                          className="arabic-text"
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="slug" className="arabic-text">الرابط المختصر *</Label>
                        <Input
                          id="slug"
                          value={formData.slug}
                          onChange={(e) => setFormData({...formData, slug: e.target.value})}
                          placeholder="مثال: home"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="target_type" className="arabic-text">نوع الهدف *</Label>
                        <Select 
                          value={formData.target_type} 
                          onValueChange={(value: 'internal' | 'external' | 'page') => 
                            setFormData({...formData, target_type: value})
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="internal">رابط داخلي</SelectItem>
                            <SelectItem value="external">رابط خارجي</SelectItem>
                            <SelectItem value="page">صفحة ديناميكية</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div>
                        <Label htmlFor="target_value" className="arabic-text">قيمة الهدف *</Label>
                        <Input
                          id="target_value"
                          value={formData.target_value}
                          onChange={(e) => setFormData({...formData, target_value: e.target.value})}
                          placeholder={
                            formData.target_type === 'internal' ? '/catalog' :
                            formData.target_type === 'external' ? 'https://example.com' :
                            'معرف الصفحة'
                          }
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="icon" className="arabic-text">الأيقونة (Lucide)</Label>
                        <Input
                          id="icon"
                          value={formData.icon}
                          onChange={(e) => setFormData({...formData, icon: e.target.value})}
                          placeholder="مثال: Home"
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="parent_id" className="arabic-text">العنصر الأب</Label>
                        <Select 
                          value={formData.parent_id} 
                          onValueChange={(value) => setFormData({...formData, parent_id: value})}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="اختر العنصر الأب (اختياري)" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="">بدون عنصر أب</SelectItem>
                            {mainItems.map((item) => (
                              <SelectItem key={item.id} value={item.id}>
                                {item.title_ar}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="is_active"
                        checked={formData.is_active}
                        onCheckedChange={(checked) => setFormData({...formData, is_active: checked})}
                      />
                      <Label htmlFor="is_active" className="arabic-text">مفعل</Label>
                    </div>
                  </TabsContent>

                  <TabsContent value="translations" className="space-y-4">
                    <div>
                      <Label htmlFor="title_en" className="arabic-text">العنوان بالإنجليزية</Label>
                      <Input
                        id="title_en"
                        value={formData.title_en}
                        onChange={(e) => setFormData({...formData, title_en: e.target.value})}
                        placeholder="Example: Home"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="title_fr" className="arabic-text">العنوان بالفرنسية</Label>
                      <Input
                        id="title_fr"
                        value={formData.title_fr}
                        onChange={(e) => setFormData({...formData, title_fr: e.target.value})}
                        placeholder="Exemple: Accueil"
                      />
                    </div>
                  </TabsContent>
                </Tabs>

                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                    إلغاء
                  </Button>
                  <Button onClick={saveMenuItem}>
                    {editingItem ? 'تحديث' : 'إضافة'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Menu Items List */}
        <Card>
          <CardHeader>
            <CardTitle className="arabic-text">عناصر القائمة الحالية</CardTitle>
            <CardDescription className="arabic-text">
              إدارة وترتيب عناصر القائمة الرئيسية
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600 arabic-text">جاري التحميل...</p>
              </div>
            ) : menuItems.length === 0 ? (
              <div className="text-center py-8">
                <Menu className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 arabic-text">لا توجد عناصر قائمة</p>
              </div>
            ) : (
              <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
              >
                <SortableContext
                  items={mainItems.map(item => item.id)}
                  strategy={verticalListSortingStrategy}
                >
                  <div className="space-y-2">
                    {mainItems.map((item) => (
                      <SortableMenuItem
                        key={item.id}
                        item={item}
                        onEdit={openEditDialog}
                        onDelete={deleteMenuItem}
                        onToggleStatus={toggleItemStatus}
                        getTargetIcon={getTargetIcon}
                        childItems={getChildItems(item.id)}
                      />
                    ))}
                  </div>
                </SortableContext>
              </DndContext>
            )}
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
